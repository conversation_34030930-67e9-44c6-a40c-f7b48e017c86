"""
Order Step Handlers.
Handles individual steps in order flows.
"""

import logging
import async<PERSON>
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from managers.ws_manager import ws_manager
from hardware.locker_control import <PERSON><PERSON><PERSON><PERSON>roller
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.section_repository import SectionRepository

logger = logging.getLogger(__name__)

# Global locker controller instance for order operations
_locker_controller = None

def get_locker_controller() -> Locker<PERSON>ontroller:
    """Get or create locker controller instance"""
    global _locker_controller
    if _locker_controller is None:
        _locker_controller = LockerController()
    return _locker_controller

async def open_door(section_id: int, is_tempered: bool = True) -> bool:
    """
    Open door for specified section

    Args:
        section_id: Section ID to open (1-206)
        is_tempered: True for tempered lock, False for non-tempered

    Returns:
        True if successful, False otherwise
    """
    try:
        controller = get_locker_controller()
        success = await controller.unlock_locker(section_id, is_tempered=is_tempered, mode="order")

        if success:
            logger.info(f"Successfully opened door for section {section_id}")
        else:
            logger.error(f"Failed to open door for section {section_id}")

        return success

    except Exception as e:
        logger.error(f"Exception opening door for section {section_id}: {e}")
        return False

async def lock_door(section_id: int, is_tempered: bool = True) -> bool:
    """
    Lock door for specified section (only for tempered locks)

    Args:
        section_id: Section ID to lock (1-206)
        is_tempered: True for tempered lock, False for non-tempered

    Returns:
        True if successful, False otherwise
    """
    try:
        controller = get_locker_controller()
        success = await controller.lock_locker(section_id, is_tempered=is_tempered, mode="order")

        if success:
            logger.info(f"Successfully locked door for section {section_id}")
        else:
            logger.error(f"Failed to lock door for section {section_id}")

        return success

    except Exception as e:
        logger.error(f"Exception locking door for section {section_id}: {e}")
        return False

async def check_door_state(section_id: int, is_tempered: bool = True) -> Optional[int]:
    """
    Check door state for specified section

    Args:
        section_id: Section ID to check (1-206)
        is_tempered: True for tempered lock, False for non-tempered

    Returns:
        1 if door is open, 0 if door is closed, None if error
    """
    try:
        controller = get_locker_controller()
        door_open = await controller.check_door_state(section_id, is_tempered=is_tempered)

        if door_open is None:
            logger.error(f"Failed to check door state for section {section_id}")
            return None

        # Convert boolean to int (1 for open, 0 for closed)
        state = 1 if door_open else 0
        logger.debug(f"Door state for section {section_id}: {state} ({'open' if state else 'closed'})")
        return state

    except Exception as e:
        logger.error(f"Exception checking door state for section {section_id}: {e}")
        return None

class StepHandler(ABC):
    """Base class for step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.logger = logging.getLogger(__name__)
    
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket connection is active"""
        return ws_manager.is_connected(self.session_id)
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False
            
        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            return False
    
    async def wait_for_websocket_ready(self, step_type: str) -> bool:
        """Wait for WebSocket ready signal"""
        try:
            # Send step ready signal
            await self.send_websocket_message({
                "type": "step_ready",
                "step": step_type,
                "message": f"Ready for {step_type}"
            })
            return True
        except Exception as e:
            logger.error(f"Error waiting for WebSocket ready: {e}")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class PickupLoopHandler(StepHandler):
    """Handler for pickup loop operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute pickup loop for multiple sections"""
        try:
            if not await self.wait_for_websocket_ready("pickup_loop"):
                return False
            
            sections = context.get('sections', [])
            operation = context.get('operation', 'pickup')
            
            await self.send_websocket_message({
                "type": "pickup_loop_started",
                "sections": sections,
                "operation": operation,
                "message": f"Starting pickup loop for {len(sections)} sections"
            })
            
            # Start pickup loop
            await self._employment_pickup_loop(sections)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in pickup loop handler: {e}")
            await self.send_websocket_message({
                "type": "pickup_loop_error",
                "message": f"Error in pickup loop: {str(e)}"
            })
            return False
    
    async def _employment_pickup_loop(self, sections: List[int]):
        """
        Employment pickup loop implementation

        This function implements the pickup loop as described in task.py:
        - Wait for messages from websocket
        - Handle "open_section" messages by opening doors and waiting for closure
        - Handle "storno" messages to stop the pickup process
        """
        picking_up = True

        # Send initial status
        await self.send_websocket_message({
            "type": "pickup_loop_ready",
            "message": "Pickup loop ready. Send 'open_section' with section_id or 'storno' to cancel.",
            "available_sections": sections
        })

        while picking_up:
            try:
                # Send status update
                await self.send_websocket_message({
                    "type": "pickup_status",
                    "message": "Waiting for section selection or storno",
                    "available_sections": sections
                })

                # In real implementation, this would wait for actual WebSocket messages
                # For now, we'll simulate the message handling
                # The actual message handling would be done through the WebSocket handler

                # This is a placeholder - in real implementation, the message handling
                # would be done through the WebSocket message queue
                await asyncio.sleep(1)

                # For demonstration, we'll break after one iteration
                # In real implementation, this would continue until "storno" message
                break

            except Exception as e:
                logger.error(f"Error in pickup loop: {e}")
                await self.send_websocket_message({
                    "type": "pickup_error",
                    "message": f"Error in pickup: {str(e)}"
                })
                break

    async def handle_pickup_message(self, message: Dict[str, Any], sections: List[int]) -> bool:
        """
        Handle messages during pickup loop

        Args:
            message: WebSocket message
            sections: Available sections for pickup

        Returns:
            True to continue pickup loop, False to stop
        """
        try:
            message_type = message.get("type")

            if message_type == "open_section":
                section_id = message.get("section_id")
                if not section_id:
                    await self.send_websocket_message({
                        "type": "error",
                        "message": "Section ID is required for open_section"
                    })
                    return True

                if section_id not in sections:
                    await self.send_websocket_message({
                        "type": "error",
                        "message": f"Section {section_id} is not available for pickup"
                    })
                    return True

                # Open the door
                success = await open_door(section_id)
                if not success:
                    await self.send_websocket_message({
                        "type": "error",
                        "message": f"Failed to open section {section_id}"
                    })
                    return True

                await self.send_websocket_message({
                    "type": "section_opened",
                    "section_id": section_id,
                    "message": f"Section {section_id} opened. Please remove item and close door."
                })

                # Wait for door to close
                door_closed = await self._wait_for_door_close(section_id)
                if door_closed:
                    await self.send_websocket_message({
                        "section_id": section_id,
                        "status": "closed"
                    })
                else:
                    await self.send_websocket_message({
                        "type": "error",
                        "message": f"Timeout waiting for door {section_id} to close"
                    })

                return True

            elif message_type == "storno":
                await self.send_websocket_message({
                    "type": "pickup_cancelled",
                    "message": "Pickup process cancelled"
                })
                return False

            else:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                return True

        except Exception as e:
            logger.error(f"Error handling pickup message: {e}")
            await self.send_websocket_message({
                "type": "pickup_error",
                "message": f"Error handling message: {str(e)}"
            })
            return True

class SectionSelectionHandler(StepHandler):
    """Handler for section selection operations"""

    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.selecting = False
        self.selected_section_ids = []
        self.operation = None
        self.phone_number = None
        self.selection_started = False

    async def handle_message(self, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message"""
        try:
            if not self.selection_started:
                logger.warning(f"Received message before selection started: {message.get('type')}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing section selection message: {message_type}")

            if message_type == "open_section":
                return await self._handle_open_section(message)
            elif message_type == "stop_selection":
                return await self._handle_stop_selection()
            else:
                logger.warning(f"Unknown message type in section selection: {message_type}")
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                return False

        except Exception as e:
            logger.error(f"Error handling message in section selection: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False

    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute section selection - start the selection process"""
        try:
            if not await self.wait_for_websocket_ready("section_selection"):
                return False

            self.operation = context.get('operation')
            self.phone_number = context.get('phone_number')
            reserved_section_id = context.get('reserved_section_id')  # Pre-reserved section from jetveo (for employee_send)
            reserved_section_ids = context.get('reserved_section_ids')  # Pre-reserved sections from jetveo (for deliver_employee)

            await self.send_websocket_message({
                "type": "section_selection_started",
                "operation": self.operation,
                "reserved_section_id": reserved_section_id,
                "reserved_section_ids": reserved_section_ids,
                "message": "Starting section selection"
            })

            # Start the selection process - send initial status and wait for user interaction
            await self.send_websocket_message({
                "type": "selection_status",
                "message": "Waiting for section selection. Send 'open_section' to select or 'stop_selection' to finish.",
                "selected_sections": self.selected_section_ids
            })

            self.selecting = True
            self.selection_started = True

            # Return False to indicate the step is not complete yet - it will complete when user finishes selection
            return False

        except Exception as e:
            logger.error(f"Error in section selection handler: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error in section selection: {str(e)}"
            })
            return False

    async def _handle_open_section(self, message: Dict[str, Any]) -> bool:
        """Handle open_section message"""
        try:
            section_id = message.get("section_id")
            if not section_id:
                await self.send_websocket_message({
                    "type": "error",
                    "message": "Section ID is required for open_section"
                })
                return False

            # Open the door using real hardware control
            await self.send_websocket_message({
                "type": "section_opening",
                "section_id": section_id,
                "message": f"Opening section {section_id}"
            })

            # Open the door
            success = await open_door(section_id)
            if not success:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Failed to open section {section_id}"
                })
                return False

            await self.send_websocket_message({
                "type": "section_opened",
                "section_id": section_id,
                "message": f"Section {section_id} opened. Please insert item and close door."
            })

            # Wait for door to be closed
            await self.send_websocket_message({
                "type": "waiting_door_close",
                "section_id": section_id,
                "message": "Waiting for door to be closed..."
            })

            # Wait for door to close
            door_closed = await self._wait_for_door_close(section_id)
            if not door_closed:
                await self.send_websocket_message({
                    "type": "error",
                    "message": f"Timeout waiting for door {section_id} to close"
                })
                return False

            await self.send_websocket_message({
                "type": "section_closed",
                "section_id": section_id,
                "message": f"Section {section_id} closed"
            })

            # Wait for insertion confirmation message
            await self.send_websocket_message({
                "type": "waiting_insertion",
                "section_id": section_id,
                "message": "Was item inserted? Send message with 'inserted': true/false"
            })

            # Note: In real implementation, this would wait for insertion confirmation message
            # For now, we assume successful insertion and add to selected sections
            self.selected_section_ids.append(section_id)

            await self.send_websocket_message({
                "type": "section_selected",
                "section_id": section_id,
                "selected_sections": self.selected_section_ids,
                "message": f"Section {section_id} selected successfully. Send 'open_section' for more or 'stop_selection' to finish."
            })

            return True

        except Exception as e:
            logger.error(f"Error handling open_section: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error opening section: {str(e)}"
            })
            return False

    async def _wait_for_door_close(self, section_id: int, timeout: int = 30) -> bool:
        """
        Wait for door to close by checking door state

        Args:
            section_id: Section ID to monitor
            timeout: Maximum time to wait in seconds

        Returns:
            True if door closed within timeout, False otherwise
        """
        try:
            start_time = asyncio.get_event_loop().time()

            while True:
                # Check if timeout exceeded
                if asyncio.get_event_loop().time() - start_time > timeout:
                    logger.warning(f"Timeout waiting for door {section_id} to close")
                    return False

                # Check door state
                door_state = await check_door_state(section_id)
                if door_state is None:
                    logger.error(f"Failed to check door state for section {section_id}")
                    await asyncio.sleep(1)
                    continue

                # If door is closed (state = 0), return success
                if door_state == 0:
                    logger.info(f"Door {section_id} closed successfully")
                    return True

                # Door is still open, wait a bit and check again
                await asyncio.sleep(0.5)

        except Exception as e:
            logger.error(f"Exception waiting for door {section_id} to close: {e}")
            return False

    async def _handle_stop_selection(self) -> bool:
        """Handle stop_selection message"""
        try:
            logger.info(f"Stop selection requested for session {self.session_id}")
            self.selecting = False

            if self.selected_section_ids:
                # Create reservations for all selected sections
                success = await self._create_reservations()

                if success:
                    await self.send_websocket_message({
                        "type": "selection_completed",
                        "selected_sections": self.selected_section_ids,
                        "message": f"Selection completed with {len(self.selected_section_ids)} sections"
                    })

                    # Determine message type based on operation
                    if self.operation == "deliver_employee":
                        message_key = "order_deliver"
                    else:  # employee_send or customer_reclaim
                        message_key = "order_send"

                    await self.send_websocket_message({
                        message_key: True,
                        "section_ids": self.selected_section_ids,
                        "message": "Order processed successfully"
                    })
                else:
                    # Determine message type based on operation
                    if self.operation == "deliver_employee":
                        message_key = "order_deliver"
                    else:  # employee_send or customer_reclaim
                        message_key = "order_send"

                    await self.send_websocket_message({
                        message_key: False,
                        "message": "Failed to create reservations"
                    })

                # Complete the step by notifying the flow coordinator
                await self._complete_step()
                return True
            else:
                await self.send_websocket_message({
                    "type": "selection_cancelled",
                    "message": "Selection cancelled - no sections selected"
                })

                # Determine message type based on operation
                if self.operation == "deliver_employee":
                    message_key = "order_deliver"
                else:  # employee_send or customer_reclaim
                    message_key = "order_send"

                await self.send_websocket_message({
                    message_key: False,
                    "message": "Section selection failed"
                })

                # Complete the step by notifying the flow coordinator
                await self._complete_step()
                return True

        except Exception as e:
            logger.error(f"Error handling stop_selection: {e}")
            await self.send_websocket_message({
                "type": "selection_error",
                "message": f"Error stopping selection: {str(e)}"
            })
            return False

    async def _create_reservations(self) -> bool:
        """Create reservations for selected sections"""
        try:
            from infrastructure.repositories.order_repository import OrderRepository

            repo = OrderRepository()
            all_results = []

            for section_id in self.selected_section_ids:
                if self.operation == "deliver_employee":
                    result = repo.create_employee_delivery_reservation(self.phone_number, section_id)
                elif self.operation == "employee_send":
                    result = repo.create_employee_send_reservation(self.phone_number, section_id)
                elif self.operation == "customer_reclaim":
                    result = repo.create_employee_send_reservation(self.phone_number, section_id)
                else:
                    result = {"success": False, "error": "Unknown operation"}

                all_results.append(result)

            # Check if all reservations were successful
            all_successful = all(result["success"] for result in all_results)
            return all_successful

        except Exception as e:
            logger.error(f"Error creating reservations: {e}")
            return False

    async def _complete_step(self):
        """Complete the current step and move to next step in flow"""
        from domains.order.flow_coordinator import flow_coordinator

        self.selection_started = False
        self.selecting = False

        # Notify the flow coordinator to complete this step and move to the next
        await flow_coordinator.complete_current_step_and_continue(self.session_id)

    async def _select_sections(self) -> tuple[bool, Optional[List[int]]]:
        """Section selection implementation - supports multiple sections"""
        selected_section_ids = []
        selecting = True

        # Send initial status
        await self.send_websocket_message({
            "type": "selection_status",
            "message": "Waiting for section selection. Send 'open_section' to select or 'stop_selection' to finish.",
            "selected_sections": selected_section_ids
        })

        while selecting:
            try:
                # Wait for message from WebSocket with timeout
                try:
                    message = await asyncio.wait_for(self.message_queue.get(), timeout=30.0)
                except asyncio.TimeoutError:
                    logger.warning(f"Section selection timeout for session {self.session_id}")
                    await self.send_websocket_message({
                        "type": "selection_timeout",
                        "message": "Selection timed out"
                    })
                    return False, None

                message_type = message.get("type")
                logger.info(f"Processing section selection message: {message_type}")

                if message_type == "open_section":
                    section_id = message.get("section_id")
                    if not section_id:
                        await self.send_websocket_message({
                            "type": "error",
                            "message": "Section ID is required for open_section"
                        })
                        continue

                    # Open the door using real hardware control
                    await self.send_websocket_message({
                        "type": "section_opening",
                        "section_id": section_id,
                        "message": f"Opening section {section_id}"
                    })

                    # Open the door
                    success = await open_door(section_id)
                    if not success:
                        await self.send_websocket_message({
                            "type": "error",
                            "message": f"Failed to open section {section_id}"
                        })
                        continue

                    await self.send_websocket_message({
                        "type": "section_opened",
                        "section_id": section_id,
                        "message": f"Section {section_id} opened. Please insert item and close door."
                    })

                    # Wait for door to close
                    door_closed = await self._wait_for_door_close(section_id)
                    if not door_closed:
                        await self.send_websocket_message({
                            "type": "error",
                            "message": f"Timeout waiting for door {section_id} to close"
                        })
                        continue

                    await self.send_websocket_message({
                        "type": "section_closed",
                        "section_id": section_id,
                        "message": f"Section {section_id} closed"
                    })

                    # Wait for insertion confirmation
                    await self.send_websocket_message({
                        "type": "waiting_insertion",
                        "section_id": section_id,
                        "message": "Waiting for item insertion..."
                    })

                    # For now, simulate successful insertion
                    # In real implementation, this would wait for door close and insertion confirmation
                    selected_section_ids.append(section_id)

                    await self.send_websocket_message({
                        "type": "section_selected",
                        "section_id": section_id,
                        "selected_sections": selected_section_ids,
                        "message": f"Section {section_id} selected successfully. Send 'open_section' for more or 'stop_selection' to finish."
                    })

                elif message_type == "stop_selection":
                    logger.info(f"Stop selection requested for session {self.session_id}")
                    selecting = False

                    if selected_section_ids:
                        await self.send_websocket_message({
                            "type": "selection_completed",
                            "selected_sections": selected_section_ids,
                            "message": f"Selection completed with {len(selected_section_ids)} sections"
                        })
                        return True, selected_section_ids
                    else:
                        await self.send_websocket_message({
                            "type": "selection_cancelled",
                            "message": "Selection cancelled - no sections selected"
                        })
                        return False, None

                else:
                    logger.warning(f"Unknown message type in section selection: {message_type}")
                    await self.send_websocket_message({
                        "type": "error",
                        "message": f"Unknown message type: {message_type}"
                    })

            except Exception as e:
                logger.error(f"Error in section selection: {e}")
                await self.send_websocket_message({
                    "type": "selection_error",
                    "message": f"Error in section selection: {str(e)}"
                })
                return False, None

        return True, selected_section_ids

class HardwareHandler(StepHandler):
    """Handler for hardware operations"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute hardware operation"""
        try:
            if not await self.wait_for_websocket_ready("hardware"):
                return False
            
            section_id = context.get('section_id')
            operation = context.get('operation', 'open_for_pickup')
            reservation_id = context.get('reservation_id')
            
            await self.send_websocket_message({
                "type": "hardware_started",
                "section_id": section_id,
                "operation": operation,
                "message": f"Starting hardware operation for section {section_id}"
            })
            
            # Open the section using the order module door control
            success = await open_door(section_id)
            
            if success:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": True,
                    "section_id": section_id,
                    "message": f"Section {section_id} opened successfully"
                })
                
                # Update reservation status if this is a pickup
                if reservation_id:
                    repo = OrderRepository()
                    repo.update_reservation_status(reservation_id, 0)  # Mark as completed
            else:
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": False,
                    "section_id": section_id,
                    "message": f"Failed to open section {section_id}"
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Error in hardware handler: {e}")
            await self.send_websocket_message({
                "type": "hardware_error",
                "message": f"Error in hardware operation: {str(e)}"
            })
            return False

def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "pickup_loop": PickupLoopHandler,
        "section_selection": SectionSelectionHandler,
        "hardware": HardwareHandler
    }
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    return None
